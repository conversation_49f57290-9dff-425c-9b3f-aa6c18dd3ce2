#!/usr/bin/env python3
"""
测试引用消息处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.message_processor import MessageProcessor

def test_extract_refer_message_content():
    """测试引用消息内容提取功能"""
    processor = MessageProcessor()
    
    # 测试用例1：正常的XML格式
    test_data_1 = {
        "messageType": "80014",
        "data": {
            "content": """<msg>
                <appmsg>
                    <refermsg>
                        <content>这是引用的原始消息内容</content>
                    </refermsg>
                </appmsg>
            </msg>""",
            "title": "这是回复的标题内容",
            "fromUser": "test_user",
            "fromGroup": "test_group"
        }
    }
    
    result_1 = processor.extract_refer_message_content(test_data_1)
    print(f"测试用例1结果:\n{result_1}")
    print("-" * 50)
    
    # 测试用例2：XML解析失败的情况
    test_data_2 = {
        "messageType": "80014",
        "data": {
            "content": "这是无效的XML内容",
            "title": "这是回复的标题内容",
            "fromUser": "test_user",
            "fromGroup": "test_group"
        }
    }
    
    result_2 = processor.extract_refer_message_content(test_data_2)
    print(f"测试用例2结果:\n{result_2}")
    print("-" * 50)
    
    # 测试用例3：只有title没有content的情况
    test_data_3 = {
        "messageType": "80014",
        "data": {
            "content": "<msg><appmsg><refermsg><content></content></refermsg></appmsg></msg>",
            "title": "只有标题内容",
            "fromUser": "test_user",
            "fromGroup": "test_group"
        }
    }
    
    result_3 = processor.extract_refer_message_content(test_data_3)
    print(f"测试用例3结果:\n{result_3}")
    print("-" * 50)

if __name__ == "__main__":
    test_extract_refer_message_content()
