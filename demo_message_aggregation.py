"""
消息聚合功能演示脚本
"""

import time
import json
from app.services.message_aggregator import MessageAggregatorService


def create_test_message(from_user: str, from_group: str, content: str) -> dict:
    """创建测试消息"""
    return {
        "messageType": "80001",
        "data": {
            "fromUser": from_user,
            "fromGroup": from_group,
            "content": content,
            "self": False,
            "wId": "test-w-id"
        }
    }


def demo_message_aggregation():
    """演示消息聚合功能"""
    print("=== 消息聚合功能演示 ===\n")
    
    # 创建消息聚合服务实例
    aggregator = MessageAggregatorService()
    aggregator.aggregation_timeout = 5  # 设置5秒超时用于演示
    
    print(f"聚合服务配置:")
    print(f"- 启用状态: {aggregator.aggregation_enabled}")
    print(f"- 超时时间: {aggregator.aggregation_timeout}秒")
    print()
    
    # 测试用户和群组
    test_user = "wxid_demo_user"
    test_group = "demo_group@chatroom"
    
    print("=== 场景1: 单条消息（不聚合）===")
    message1 = create_test_message(test_user, test_group, "这是一条单独的消息")
    should_process, aggregated_data = aggregator.add_message_to_aggregation(message1)
    print(f"消息: {message1['data']['content']}")
    print(f"是否立即处理: {should_process}")
    print(f"聚合数据: {'有' if aggregated_data else '无'}")
    print()
    
    # 等待一下确保之前的聚合被清理
    time.sleep(1)
    
    print("=== 场景2: 多条消息聚合 ===")
    messages = [
        "第一条消息",
        "第二条消息", 
        "第三条消息"
    ]
    
    print("发送多条消息...")
    for i, content in enumerate(messages):
        message = create_test_message(test_user, test_group, content)
        should_process, aggregated_data = aggregator.add_message_to_aggregation(message)
        print(f"消息{i+1}: {content}")
        print(f"  是否立即处理: {should_process}")
        print(f"  聚合数据: {'有' if aggregated_data else '无'}")
        
        # 在消息之间稍微等待一下
        time.sleep(0.5)
    
    print("\n查看聚合状态:")
    status = aggregator.get_aggregation_status()
    print(f"活跃聚合数量: {status['active_aggregations']}")
    
    if status['aggregations']:
        for key, info in status['aggregations'].items():
            print(f"聚合键: {key}")
            print(f"  用户: {info['from_user']}")
            print(f"  群组: {info['from_group']}")
            print(f"  消息数量: {info['message_count']}")
            print(f"  已等待时间: {info['elapsed_time']:.1f}秒")
    
    print(f"\n等待{aggregator.aggregation_timeout}秒让聚合超时...")
    
    # 模拟等待聚合超时
    start_time = time.time()
    while time.time() - start_time < aggregator.aggregation_timeout + 1:
        time.sleep(0.5)
        current_status = aggregator.get_aggregation_status()
        if current_status['active_aggregations'] == 0:
            print("聚合已处理完成！")
            break
    else:
        print("聚合超时处理")
    
    print("\n=== 场景3: 强制处理聚合 ===")
    
    # 添加新消息
    message = create_test_message(test_user, test_group, "需要强制处理的消息")
    should_process, _ = aggregator.add_message_to_aggregation(message)
    print(f"添加消息: {message['data']['content']}")
    print(f"是否立即处理: {should_process}")
    
    # 强制处理
    print("强制处理聚合...")
    result = aggregator.force_process_aggregation(test_user, test_group)
    print(f"强制处理结果: {result}")
    
    # 检查最终状态
    final_status = aggregator.get_aggregation_status()
    print(f"\n最终聚合状态:")
    print(f"活跃聚合数量: {final_status['active_aggregations']}")
    
    # 停止服务
    aggregator.stop()
    print("\n消息聚合服务已停止")
    print("\n=== 演示完成 ===")


if __name__ == "__main__":
    demo_message_aggregation()
