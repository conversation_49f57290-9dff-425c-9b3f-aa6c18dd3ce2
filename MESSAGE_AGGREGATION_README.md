# 消息聚合功能说明

## 功能概述

消息聚合功能实现了同一群组中同一用户短时间内多条消息的自动合并，避免频繁调用AI接口，提高处理效率和用户体验。

## 工作原理

1. **消息接收**: 当收到群聊消息时，系统首先判断是否需要进行聚合
2. **聚合判断**: 基于用户ID和群组ID创建聚合键，将相同用户在同一群组的消息聚合在一起
3. **计时器机制**: 从用户第一条消息开始计时，默认15秒内如果有新消息则重置计时器
4. **消息合并**: 超时后将所有聚合的消息按时间顺序合并成一条消息发送给AI
5. **立即处理**: 如果15秒内没有新消息，则自动处理聚合的消息

## 配置说明

### 配置文件 (config.json)

```json
{
  "message_aggregation": {
    "enabled": true,           // 是否启用消息聚合功能
    "timeout_seconds": 15      // 聚合超时时间（秒）
  }
}
```

### 配置参数说明

- `enabled`: 控制是否启用消息聚合功能
  - `true`: 启用聚合功能
  - `false`: 禁用聚合功能，消息将立即处理
  
- `timeout_seconds`: 聚合超时时间
  - 默认值: 15秒
  - 建议范围: 5-30秒
  - 过短可能导致聚合效果不明显
  - 过长可能影响响应速度

## 使用场景

### 适用场景
- 用户连续发送多条短消息
- 用户发送长消息时分段发送
- 用户补充或修正之前的消息
- 减少AI接口调用频率

### 不适用场景
- 紧急消息需要立即响应
- 单条完整消息
- 不同用户的消息（自动按用户分组）

## API接口

### 查看聚合状态
```http
GET /api/v1/aggregation/status
```

响应示例:
```json
{
  "success": true,
  "message": "获取聚合状态成功",
  "data": {
    "enabled": true,
    "timeout": 15,
    "active_aggregations": 1,
    "aggregations": {
      "group123@chatroom:wxid_user123": {
        "from_user": "wxid_user123",
        "from_group": "group123@chatroom",
        "message_count": 3,
        "first_message_time": **********.123,
        "last_message_time": **********.456,
        "elapsed_time": 8.5
      }
    }
  }
}
```

### 强制处理聚合
```http
POST /api/v1/aggregation/force-process
Content-Type: application/json

{
  "from_user": "wxid_user123",
  "from_group": "group123@chatroom"
}
```

### 获取聚合配置
```http
GET /api/v1/aggregation/config
```

### 健康检查
```http
GET /api/v1/aggregation/health
```

## 实现细节

### 核心组件

1. **MessageAggregatorService**: 消息聚合服务主类
2. **MessageAggregation**: 单个聚合对象，管理特定用户和群组的消息
3. **PendingMessage**: 待聚合的消息数据结构

### 关键特性

- **线程安全**: 使用锁机制确保并发安全
- **自动清理**: 聚合完成后自动清理资源
- **错误处理**: 完善的异常处理和日志记录
- **状态监控**: 提供实时状态查询接口
- **强制处理**: 支持手动触发聚合处理

### 消息流程

```
消息接收 -> 聚合判断 -> 添加到聚合 -> 启动/重置定时器 -> 超时处理 -> 发送给AI
    |                                                      ^
    |                                                      |
    +-- 不需要聚合 -> 立即处理 ---------------------------+
```

## 监控和调试

### 日志级别
- `INFO`: 聚合操作的关键信息
- `DEBUG`: 详细的聚合状态变化
- `ERROR`: 聚合过程中的错误

### 关键日志示例
```
INFO - 消息已添加到聚合: user=wxid_user123, group=group123@chatroom, total_messages=2, timeout=15s
INFO - 处理聚合消息: user=wxid_user123, group=group123@chatroom, message_count=3, aggregated_content_length=45
DEBUG - 聚合数据已清理: group123@chatroom:wxid_user123
```

## 性能考虑

### 内存使用
- 每个活跃聚合占用少量内存
- 聚合完成后立即释放资源
- 建议监控活跃聚合数量

### 定时器管理
- 每个聚合使用一个定时器
- 定时器在聚合完成后自动清理
- 应用关闭时会清理所有定时器

## 故障排除

### 常见问题

1. **聚合不生效**
   - 检查配置文件中 `enabled` 是否为 `true`
   - 确认消息类型为群聊消息 (messageType: "80001")
   - 检查消息是否包含必要字段

2. **聚合超时时间不准确**
   - 检查系统时间是否正确
   - 确认配置文件中的 `timeout_seconds` 设置
   - 查看日志中的实际处理时间

3. **内存占用过高**
   - 检查活跃聚合数量
   - 确认聚合是否正常完成和清理
   - 考虑调整超时时间

### 调试步骤

1. 查看聚合状态: `GET /api/v1/aggregation/status`
2. 检查应用日志中的聚合相关信息
3. 使用强制处理接口测试特定聚合
4. 运行演示脚本验证功能: `python demo_message_aggregation.py`

## 测试

运行测试套件:
```bash
python -m pytest tests/test_message_aggregator.py -v
```

运行演示脚本:
```bash
python demo_message_aggregation.py
```

## 版本历史

- v1.0.0: 初始版本，实现基本的消息聚合功能
  - 支持基于用户和群组的消息聚合
  - 可配置的超时时间
  - 完整的API接口
  - 全面的测试覆盖
