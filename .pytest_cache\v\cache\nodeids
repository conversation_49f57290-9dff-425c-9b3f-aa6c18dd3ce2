["tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_add_friends_exception_handling", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_add_friends_to_ignore_list_empty", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_add_friends_to_ignore_list_success", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_clear_ignore_list", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_get_ignore_list", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_get_ignore_list_count", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_get_ignore_status_info", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_is_friend_ignored_disabled", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_is_friend_ignored_exception_handling", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_is_friend_ignored_false", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_is_friend_ignored_nickname_whitelist", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_is_friend_ignored_true", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_is_friend_ignored_whitelist", "tests/test_friend_ignore_service.py::TestFriendIgnoreService::test_remove_friend_from_ignore_list", "tests/test_message_aggregator.py::TestMessageAggregation::test_add_message", "tests/test_message_aggregator.py::TestMessageAggregation::test_clear", "tests/test_message_aggregator.py::TestMessageAggregation::test_get_aggregated_content", "tests/test_message_aggregator.py::TestMessageAggregation::test_get_latest_message_data", "tests/test_message_aggregator.py::TestMessageAggregation::test_message_aggregation_creation", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_add_message_to_aggregation_first_message", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_add_message_to_aggregation_multiple_messages", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_add_message_to_aggregation_no_aggregation", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_force_process_aggregation", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_force_process_aggregation_not_found", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_get_aggregation_status", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_process_aggregated_messages_timeout", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_should_aggregate_message_disabled", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_should_aggregate_message_missing_fields", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_should_aggregate_message_self_message", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_should_aggregate_message_valid", "tests/test_message_aggregator.py::TestMessageAggregatorService::test_should_aggregate_message_wrong_type", "tests/test_message_aggregator.py::TestPendingMessage::test_pending_message_creation", "tests/test_message_processor.py::TestMessageProcessor::test_enqueue_callback_message_invalid", "tests/test_message_processor.py::TestMessageProcessor::test_enqueue_callback_message_success", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_friend_ignored", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_friend_ignored_activate_silence", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_friend_ignored_extend_silence", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_friend_ignored_in_silence_mode", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_friend_not_ignored", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_missing_fields", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_self_sent", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_silence_active", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_success", "tests/test_message_processor.py::TestMessageProcessor::test_is_valid_group_message_wrong_type", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_dynamic_w_id_update", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_ecloud_query_online_wechat_list", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_email_service_connection_handling", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_email_service_disabled", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_online_status_worker_disabled", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_online_status_worker_status", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_sms_service_disabled", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_sms_service_generate_sign", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_startup_contact_sync_logic", "tests/test_online_status_monitor.py::TestOnlineStatusMonitor::test_w_id_update_in_online_status_check", "tests/test_silence_service.py::TestSilenceService::test_activate_silence_mode_disabled", "tests/test_silence_service.py::TestSilenceService::test_activate_silence_mode_success", "tests/test_silence_service.py::TestSilenceService::test_deactivate_silence_mode", "tests/test_silence_service.py::TestSilenceService::test_extend_silence_mode", "tests/test_silence_service.py::TestSilenceService::test_get_silence_remaining_time", "tests/test_silence_service.py::TestSilenceService::test_get_silence_remaining_time_inactive", "tests/test_silence_service.py::TestSilenceService::test_get_silence_status", "tests/test_silence_service.py::TestSilenceService::test_get_silence_status_active", "tests/test_silence_service.py::TestSilenceService::test_is_silence_active", "tests/test_silence_service.py::TestSilenceService::test_is_silence_active_disabled"]